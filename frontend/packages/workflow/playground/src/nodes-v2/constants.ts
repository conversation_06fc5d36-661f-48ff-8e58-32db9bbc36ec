/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { VARIABLE_MERGE_NODE_REGISTRY } from '@/nodes-v2/variable-merge';
import { VARIABLE_ASSIGN_NODE_REGISTRY } from '@/nodes-v2/variable-assign';
import { LLM_NODE_REGISTRY } from '@/nodes-v2/llm';
import {
  CODE_NODE_REGISTRY,
  COMMENT_NODE_REGISTRY,
  DATABASE_CREATE_NODE_REGISTRY,
  DATABASE_DELETE_NODE_REGISTRY,
  DAT<PERSON><PERSON><PERSON>_NODE_REGISTRY,
  DATABASE_QUERY_NODE_REGISTRY,
  DATABASE_UPDATE_NODE_REGISTRY,
  DATASET_NODE_REGISTRY,
  DATASET_WRITE_NODE_REGISTRY,
  END_NODE_REGISTRY,
  HTTP_NODE_REGISTRY,
  IMAGE_CANVAS_NODE_REGISTRY,
  IMAGE_GENERATE_NODE_REGISTRY,
  IMAGE_REFERENCE_NODE_REGISTRY,
  INPUT_NODE_REGISTRY,
  LOOP_NODE_REGISTRY,
  LTM_NODE_REGISTRY,
  OUTPUT_NODE_REGISTRY,
  QUESTION_NODE_REGISTRY,
  START_NODE_REGISTRY,
  TEXT_PROCESS_NODE_REGISTRY,
  TRIGGER_DELETE_NODE_REGISTRY,
  TRIGGER_READ_NODE_REGISTRY,
  TRIGGER_UPSERT_NODE_REGISTRY,
  BREAK_NODE_REGISTRY,
  CONTINUE_NODE_REGISTRY,
  SET_VARIABLE_NODE_REGISTRY,
  BATCH_NODE_REGISTRY,
  INTENT_NODE_REGISTRY,
  IF_NODE_REGISTRY,
  PLUGIN_NODE_REGISTRY,
  SUB_WORKFLOW_NODE_REGISTRY,
  VARIABLE_NODE_REGISTRY,
  JSON_STRINGIFY_NODE_REGISTRY,
  // CLI script insert ID (import), do not modify/delete this line comment
} from '@/node-registries';

import {
  CLEAR_CONTEXT_NODE_REGISTRY,
  CREATE_CONVERSATION_NODE_REGISTRY,
  QUERY_MESSAGE_LIST_NODE_REGISTRY,
  UPDATE_CONVERSATION_NODE_REGISTRY,
  DELETE_CONVERSATION_NODE_REGISTRY,
  QUERY_CONVERSATION_LIST_NODE_REGISTRY,
  QUERY_CONVERSATION_HISTORY_NODE_REGISTRY,
  CREATE_MESSAGE_NODE_REGISTRY,
  UPDATE_MESSAGE_NODE_REGISTRY,
  DELETE_MESSAGE_NODE_REGISTRY,
} from './chat';

export const NODES_V2 = [
  // The cli script inserts the identifier (registry), do not modify/delete this line comment
  JSON_STRINGIFY_NODE_REGISTRY,
  IF_NODE_REGISTRY,
  INTENT_NODE_REGISTRY,
  SUB_WORKFLOW_NODE_REGISTRY,
  CODE_NODE_REGISTRY,
  LTM_NODE_REGISTRY,
  IMAGE_GENERATE_NODE_REGISTRY,
  IMAGE_REFERENCE_NODE_REGISTRY,
  TRIGGER_READ_NODE_REGISTRY,
  TRIGGER_DELETE_NODE_REGISTRY,
  IMAGE_CANVAS_NODE_REGISTRY,
  OUTPUT_NODE_REGISTRY,
  END_NODE_REGISTRY,
  INPUT_NODE_REGISTRY,
  START_NODE_REGISTRY,
  TRIGGER_UPSERT_NODE_REGISTRY,
  PLUGIN_NODE_REGISTRY,
  LLM_NODE_REGISTRY,
  HTTP_NODE_REGISTRY,
  LOOP_NODE_REGISTRY,
  SET_VARIABLE_NODE_REGISTRY,
  CONTINUE_NODE_REGISTRY,
  BREAK_NODE_REGISTRY,
  BATCH_NODE_REGISTRY,
  COMMENT_NODE_REGISTRY,
  CREATE_CONVERSATION_NODE_REGISTRY,
  CLEAR_CONTEXT_NODE_REGISTRY,
  QUERY_MESSAGE_LIST_NODE_REGISTRY,
  UPDATE_CONVERSATION_NODE_REGISTRY,
  DELETE_CONVERSATION_NODE_REGISTRY,
  QUERY_CONVERSATION_LIST_NODE_REGISTRY,
  QUERY_CONVERSATION_HISTORY_NODE_REGISTRY,
  CREATE_MESSAGE_NODE_REGISTRY,
  UPDATE_MESSAGE_NODE_REGISTRY,
  DELETE_MESSAGE_NODE_REGISTRY,
  VARIABLE_MERGE_NODE_REGISTRY,
  VARIABLE_ASSIGN_NODE_REGISTRY,
  DATABASE_CREATE_NODE_REGISTRY,
  DATABASE_QUERY_NODE_REGISTRY,
  DATABASE_DELETE_NODE_REGISTRY,
  DATABASE_UPDATE_NODE_REGISTRY,
  QUESTION_NODE_REGISTRY,
  DATASET_WRITE_NODE_REGISTRY,
  DATASET_NODE_REGISTRY,
  TEXT_PROCESS_NODE_REGISTRY,
  DATABASE_NODE_REGISTRY,
  VARIABLE_NODE_REGISTRY,
];
export const NODE_V2_TYPES = NODES_V2.map(r => r.type);
