/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import {
  ArrayType,
  type BaseVariableField,
} from '@flowgram-adapter/free-layout-editor';

export class CustomArrayType extends ArrayType {
  getByKeyPath(keyPath: string[]): BaseVariableField<unknown> | undefined {
    // const [curr, ...rest] = keyPath || [];

    // if (curr === '0' && this.canDrilldownItems) {
    //   //Array item 0
    //   return this.items.getByKeyPath(rest);
    // }

    if (this.canDrilldownItems) {
      // The bottom line in Coze is item 0
      return this.items.getByKeyPath(keyPath);
    }

    return;
  }
}
