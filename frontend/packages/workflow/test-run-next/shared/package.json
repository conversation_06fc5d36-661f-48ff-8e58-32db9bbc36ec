{"name": "@coze-workflow/test-run-shared", "version": "0.0.1", "description": "Workflow TestRun 公共包", "author": "<EMAIL>", "exports": {".": "./src/index.ts"}, "main": "./src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest run --passWithNoTests", "test:cov": "vitest run --passWithNoTests --coverage"}, "dependencies": {"@codemirror/language": "^6.10.1", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.34.1", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-editor/editor": "0.1.0-alpha.dd871b", "@lezer/common": "^1.2.2", "ahooks": "^3.7.8", "clsx": "^1.2.1", "lodash-es": "^4.17.21"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "typescript": "~5.8.2", "vitest": "~3.0.5"}}