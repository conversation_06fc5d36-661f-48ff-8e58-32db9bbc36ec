/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

// import { domUtils } from '@flowgram-adapter/common';
import { type FlowSelectorBoundsLayerOptions } from '@flowgram-adapter/free-layout-editor';
import { SelectionService, useService } from '@flowgram-adapter/free-layout-editor';

import { getSelectionBounds } from '../../utils/selection-utils';

import styles from './index.module.less';

/**
 * select box
 * @param props
 * @constructor
 */
export const SelectorBounds: React.FC<
  FlowSelectorBoundsLayerOptions
> = props => {
  const selectService = useService<SelectionService>(SelectionService);
  const bounds = getSelectionBounds(selectService, true);
  if (bounds.width === 0 || bounds.height === 0) {
    // domUtils.setStyle(domNode, {
    //   display: 'none',
    // });
    return <></>;
  }
  const style = {
    display: 'block',
    left: bounds.left,
    top: bounds.top,
    width: bounds.width,
    height: bounds.height,
  };
  // domUtils.setStyle(domNode, style);
  return <div className={styles.selectorBoundsForground} style={style} />;
};
