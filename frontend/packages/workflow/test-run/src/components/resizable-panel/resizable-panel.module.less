.container {
  display: flex;
  flex-direction: column;
}

.resizable-panel {
  position: absolute;
  left: 0;
  bottom: 0;

  overflow: hidden;

  width: 100%;
  max-height: 90%;

  background: rgb(var(--coze-fg-white));
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -8px 24px 0 rgba(0, 0, 0, 16%), 0 -16px 48px 0 rgba(0, 0, 0, 8%);
  z-index: 10;
  &.need-transition {
    transition: height 0.25s;
  }
}

.resizable-panel-translateY {
  position: absolute;
  left: 0;
  bottom: 0;

  overflow: hidden;

  width: 100%;
  max-height: calc(100% - 52px);

  background: rgb(var(--coze-fg-white));
  border-radius: 8px 8px 0 0;
  box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.1);
  z-index: 10;

  &.show {
    animation: showAnimation 0.25s cubic-bezier(0.14, 1, 0.34, 1);
  }

  &.hide {
    animation: hideAnimation 0.25s cubic-bezier(0.34, 1, 0.14, 1);
  }

  @keyframes showAnimation {
    0% {
      transform: translateY(20%);
      opacity: 0;
    }
    100% {
      transform: translateY(0%);
      opacity: 1;
    }
  }

  @keyframes hideAnimation {
    0% {
      transform: translateY(0%);
      opacity: 1;
    }
    100% {
      transform: translateY(20%);
      opacity: 0;
    }
  }
}


.resizable-panel-slide {
  position: absolute;
  bottom: 0;
  right: -100%; /* The initial position is on the right outside of the container */

  overflow: hidden;

  width: 100%;
  height: 100%;

  background: rgb(var(--coze-fg-white));
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -8px 24px 0 rgba(0, 0, 0, 16%), 0 -16px 48px 0 rgba(0, 0, 0, 8%);
  z-index: 10;
  transition: transform 0.25s ease;

  &.slide-in {
    transform: translateX(-100%);
  }

  &.slide-out {
    transform: translateX(100%);
  }
}



.panel-dragging {
  cursor: row-resize;

  position: absolute;
  top: 0;
  left: 0;

  width: 100%;
  height: 10px;

  &::before {
    content: "";

    position: absolute;
    top: 4px;
    left: calc((100% - 100px) / 2);

    display: block;

    width: 100px;
    height: 4px;

    background-color: var(--coz-stroke-primary);
    border-radius: 4px;
  }
}

.panel-header {
  height: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px 0 12px;
  border-bottom: 1px solid var(--coz-stroke-primary);
}

.panel-footer {
  height: 56px;
  min-height: 56px;
  max-height: 56px;
  border-top: 1px solid var(--coz-stroke-primary);
  flex-grow: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px 0 12px;
}

.panel-content {
  //height: calc(100% - 104px);
  flex: 1;
  //flex-shrink: 1;
  //flex-grow: 1;
  overflow-y: auto;
}
