/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import {
  createWithEqualityFn,
  type UseBoundStoreWithEqualityFn,
} from 'zustand/traditional';
import { shallow } from 'zustand/shallow';
import { type StoreApi } from 'zustand';

export interface DataViewerState {
  // Folded and unfolded state
  expand: Record<string, boolean> | null;
  setExpand: (key: string, v: boolean) => void;
}

export type DataViewerStore = UseBoundStoreWithEqualityFn<
  StoreApi<DataViewerState>
>;

export const createDataViewerStore = () =>
  createWithEqualityFn<DataViewerState>(
    set => ({
      expand: null,
      setExpand: (key: string, v: boolean) => {
        set(state => ({
          expand: {
            ...state.expand,
            [key]: v,
          },
        }));
      },
    }),
    shallow,
  );
